import numpy as np

from env import UnmannedClusterEnv
import pygame
import time
import math

def main():
    env = UnmannedClusterEnv()
    env.auto_coordination = True  # 启用自动协调
    obs = env.reset()
    
    # 初始化搜索策略
    env._initialize_search_strategy()
    
    # 初始化渲染
    env.render()
    
    done = False
    step_count = 0
    
    while not done:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                env.close()
                return
        
        # 初始化动作数组
        action = np.zeros(env.action_space.shape[0])
        action_idx = 0
        
        # 控制白方无人艇 - 简单跟随无人机当前位置
        for i, boat in enumerate(env.white_boats):
            if not boat.active or boat.frozen_time > 0:
                action_idx += 2
                continue

            # 获取配对的无人机
            paired_drone = boat.paired_drone if hasattr(boat, 'paired_drone') else None
            if paired_drone is None and i < len(env.white_drones):
                paired_drone = env.white_drones[i]

            # 简单跟随：直接朝无人机当前位置移动
            if paired_drone and paired_drone.active:
                # 计算到无人机的方向和距离
                direction_to_drone = paired_drone.pos - boat.pos
                distance_to_drone = np.linalg.norm(direction_to_drone)
                
                if distance_to_drone > 2.0:  # 距离大于2km时移动
                    target_heading = math.atan2(direction_to_drone[1], direction_to_drone[0])
                    target_speed = boat.max_speed * 0.8  # 80%速度跟随
                else:
                    # 距离很近时保持当前状态
                    target_heading = boat.heading
                    target_speed = boat.max_speed * 0.3
                    
                print(f"Step {step_count}: B{i+1} 跟随 D{i+1} - 距离:{distance_to_drone:.1f}, 速度:{target_speed:.2f}")
            else:
                # 没有配对无人机时停止
                target_heading = boat.heading
                target_speed = 0.0
                print(f"Step {step_count}: B{i+1} 无配对无人机")

            # 转换为动作格式
            action[action_idx] = (target_speed / boat.max_speed) * 2 - 1  # 归一化到[-1,1]
            action[action_idx + 1] = target_heading / math.pi  # 归一化到[-1,1]
            action_idx += 2
        
        # 控制白方无人机 - 与配对无人艇协同的智能策略
        for i, drone in enumerate(env.white_drones):
            if not drone.active:
                action_idx += 2
                continue

            # 获取配对的无人艇
            paired_boat = drone.paired_boat if hasattr(drone, 'paired_boat') else None
            if paired_boat is None and i < len(env.white_boats):
                paired_boat = env.white_boats[i]

            if drone.on_boat:
                # 快速起飞策略 - 确保分散搜索
                should_takeoff = False

                # 更激进的起飞策略，确保快速分散
                if step_count < 10:  # 前10步立即起飞
                    should_takeoff = drone.energy > 0.7  # 降低能量要求
                elif step_count < 30:  # 前30步内必须起飞
                    should_takeoff = drone.energy > 0.6
                elif len(env.priority_targets) > 0:  # 发现目标时
                    should_takeoff = drone.energy > 0.5
                elif step_count < 50 and drone.energy > 0.8:  # 前50步且能量充足
                    should_takeoff = True
                elif drone.energy > 0.9:  # 能量充足时
                    should_takeoff = True

                if should_takeoff:
                    action[action_idx] = 1.0  # 起飞
                    # 确保无人机知道其分配的搜索区域
                    if hasattr(drone, 'zone_id') and drone.zone_id is not None:
                        # 无人机已经在初始化时分配了区域
                        pass
                    elif paired_boat and hasattr(paired_boat, 'assigned_zone'):
                        zone = paired_boat.assigned_zone
                        if zone:
                            drone.initialize_search_zone(zone['bounds'], 'spiral')
                else:
                    action[action_idx] = -1.0  # 继续充电
                action[action_idx + 1] = 0.0
            else:
                # 无人机在空中执行任务
                # 智能能量管理 - 考虑任务紧急程度
                energy_strategy = 'normal'
                if len(env.priority_targets) > 0:
                    energy_strategy = 'aggressive'  # 有目标时采用激进策略
                elif len(env.black_boats) == 0:
                    energy_strategy = 'conservative'  # 没有目标时保守策略

                if drone.should_return_for_charging(env.white_boats):
                    # 返回配对的无人艇充电
                    if paired_boat and paired_boat.active:
                        return_vec = paired_boat.pos - drone.pos
                        return_distance = np.linalg.norm(return_vec)

                        if return_distance > 0.5:
                            target_heading = math.atan2(return_vec[1], return_vec[0])
                            target_speed = drone.max_speed
                        else:
                            target_heading = drone.heading
                            target_speed = drone.min_speed

                        # 应用能量优化
                        target_heading, target_speed = drone.optimize_flight_pattern_for_energy(target_heading, target_speed)

                        action[action_idx] = (target_speed - drone.min_speed) / (drone.max_speed - drone.min_speed) * 2 - 1
                        action[action_idx + 1] = target_heading / math.pi
                    else:
                        # 配对无人艇不可用，寻找最近的无人艇
                        nearest_boat = min([b for b in env.white_boats if b.active],
                                         key=lambda b: np.linalg.norm(drone.pos - b.pos))
                        return_vec = nearest_boat.pos - drone.pos
                        target_heading = math.atan2(return_vec[1], return_vec[0])
                        target_speed = drone.max_speed

                        action[action_idx] = (target_speed - drone.min_speed) / (drone.max_speed - drone.min_speed) * 2 - 1
                        action[action_idx + 1] = target_heading / math.pi
                else:
                    # 执行固定巡逻点巡逻任务
                    # 无人机在空中时，按照固定的10个巡逻点进行循环巡逻
                    if hasattr(drone, 'patrol_waypoints') and drone.patrol_waypoints:
                        # 执行固定巡逻路径
                        patrol_heading, patrol_speed = drone._execute_patrol_path()

                        if patrol_heading is not None and patrol_speed is not None:
                            # 使用巡逻计算的朝向和速度
                            action[action_idx] = (patrol_speed - drone.min_speed) / (drone.max_speed - drone.min_speed) * 2 - 1
                            action[action_idx + 1] = patrol_heading / math.pi
                        else:
                            # 备用：朝第一个巡逻点移动
                            if len(drone.patrol_waypoints) > 0:
                                target_point = drone.patrol_waypoints[0]
                                direction = target_point - drone.pos
                                if np.linalg.norm(direction) > 0:
                                    target_heading = math.atan2(direction[1], direction[0])
                                    target_speed = drone.max_speed * 0.7
                                    action[action_idx] = (target_speed - drone.min_speed) / (drone.max_speed - drone.min_speed) * 2 - 1
                                    action[action_idx + 1] = target_heading / math.pi
                                else:
                                    action[action_idx] = 0.5
                                    action[action_idx + 1] = 0.0
                            else:
                                action[action_idx] = 0.5
                                action[action_idx + 1] = 0.0
                    else:
                        # 没有巡逻点，执行默认搜索
                        action[action_idx] = 0.6
                        action[action_idx + 1] = 0.0

            action_idx += 2
        
        # 执行环境步进
        obs, reward, done, info = env.step(action)

        # 注意：跨平台探测和搜索模式更新已在env.step()中处理
        
        # 渲染
        env.render()
        
        # 打印协同状态信息
        if step_count % 50 == 0:  # 每50步打印一次
            active_drones = len([d for d in env.white_drones if d.active and not d.on_boat])
            tracking_drones = len([d for d in env.white_drones if d.active and d.tracking_mode])
            support_drones = len([d for d in env.white_drones if d.active and d.support_mode])
            active_boats = len([b for b in env.white_boats if b.active])

            detected_by_boats = sum(len(boat.detect(env.black_boats)) for boat in env.white_boats)
            detected_by_drones = sum(len(drone.detect(env.black_boats)) for drone in env.white_drones if drone.active and not drone.on_boat)
            total_detected = detected_by_boats + detected_by_drones

            # 统计配对状态
            paired_count = 0
            for boat in env.white_boats:
                if hasattr(boat, 'paired_drone') and boat.paired_drone and boat.paired_drone.active:
                    paired_count += 1

            # 统计锁定状态
            locking_boats = len([b for b in env.white_boats if b.lock_target is not None])
            black_boat_status = ""
            if env.black_boats:
                black_boat = env.black_boats[0]
                black_boat_status = f"黑船状态: 位置({black_boat.pos[0]:.1f},{black_boat.pos[1]:.1f}), 被锁定{black_boat.lock_count}次"

            print(f"Step {step_count}: 5v5v1协同围捕状态")
            print(f"  白方: 活跃无人机{active_drones}/5, 跟踪中{tracking_drones}, 支援中{support_drones}, 活跃无人艇{active_boats}/5, 配对{paired_count}/5")
            print(f"  探测: 无人艇发现{detected_by_boats}, 无人机发现{detected_by_drones}, 总计{total_detected}")
            print(f"  锁定: {locking_boats}艇正在锁定, {black_boat_status}")
            print(f"  奖励: {reward:.1f}")
            print("-" * 60)
        
        step_count += 1
        time.sleep(0.03)  # 减慢速度以便观察
        
        # 检查是否结束
        if done:
            print("Episode finished!")
            print(f"Total steps: {step_count}")
            print(f"Final reward: {reward}")
            break
    
    # 保持窗口打开直到用户关闭
    while True:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                env.close()
                return
        env.render()

if __name__ == "__main__":
    main()
