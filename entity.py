import numpy as np
import math
from typing import List
import random

# 实体类
class Entity:
    def __init__(self, pos: np.n<PERSON>ray, vel: float, heading: float, min_turn_radius: float, max_speed: float, min_speed: float = 0):
        self.pos = pos.astype(float)
        self.vel = vel
        self.heading = heading  # 弧度
        self.min_turn_radius = min_turn_radius
        self.max_speed = max_speed
        self.min_speed = min_speed

    def update_position(self, dt: float, target_heading: float, target_speed: float):
        # 简单运动学模型，考虑最小转弯半径（使用Dubins-like更新）
        angular_vel = (target_heading - self.heading) / dt if abs(target_heading - self.heading) < abs(2*math.pi+target_heading-self.heading) else (2*math.pi+target_heading-self.heading) / dt
        max_angular_vel = self.vel / self.min_turn_radius if self.vel > 0 else 0
        angular_vel = np.clip(angular_vel, -max_angular_vel, max_angular_vel)
        self.heading += angular_vel * dt
        self.vel = np.clip(target_speed, self.min_speed, self.max_speed)
        self.pos += np.array([self.vel * math.cos(self.heading), self.vel * math.sin(self.heading)]) * dt

class Boat(Entity):
    def __init__(self, pos, vel=0, heading=0):
        super().__init__(pos, vel, heading, min_turn_radius=0.02, max_speed=0.01, min_speed=0)
        self.detect_range = 50  # 增加探测范围到50km
        self.side = 'white'
        self.lock_range = 100   # 增加锁定范围到100km
        self.lock_target = None
        self.lock_time = 0
        self.lock_success = 0
        self.frozen = False
        self.lock_count = 0
        self.frozen_time = 0
        self.exited = False
        self.active = True
        self.assigned_zone = None
        self.patrol_waypoints = []
        self.current_waypoint = 0
        self.search_pattern = 'sweep'  # 'sweep', 'spiral', 'grid'
        self.paired_drone = None  # 配对的无人机

    # def detect(self, targets):
    #     # 圆形探测
    #     detected = [t for t in targets if np.linalg.norm(self.pos - t.pos) < self.detect_range]
    #     return detected
    def detect(self, targets):
        detected = []
        for target in targets:
            if target.exited:
                continue
            if np.linalg.norm(self.pos - target.pos) < self.detect_range:
                detected.append(target)
        return detected

    def be_locked(self):
        self.lock_count += 1
        if self.lock_count == 1:
            self.frozen_time = 300
        elif self.lock_count >= 2:
            self.exited = True

    def lock(self, target: 'Boat', dt):
        if self.frozen_time > 0 or self.lock_count >= 2:
            return False
        if self.lock_target != target:
            self.lock_time = 0
            self.lock_target = target
        if np.linalg.norm(self.pos - target.pos) > self.lock_range:
            self.lock_target = None
            self.lock_time = 0
            return False
        self.lock_time += dt
        if self.lock_time > 100:  # 减少到100秒（约1.7分钟）以便更快看到效果
            if random.random() < 0.8:
                target.be_locked()
                self.lock_success += 1 if hasattr(self, 'side') and self.side == 'white' else 0
                self.lock_target = None
                self.lock_time = 0
                return True
            else:
                self.lock_time = 0
                
        return False

    def update_search_behavior(self, dt):
        """更新搜索行为"""
        if not self.assigned_zone or not self.patrol_waypoints:
            return None, None
            
        # 获取当前目标点
        if self.current_waypoint < len(self.patrol_waypoints):
            target_pos = self.patrol_waypoints[self.current_waypoint]
            
            # 检查是否到达当前路径点
            if np.linalg.norm(self.pos - target_pos) < 2.0:  # 2km容差
                self.current_waypoint = (self.current_waypoint + 1) % len(self.patrol_waypoints)
                
            # 计算朝向目标的方向
            direction = target_pos - self.pos
            target_heading = math.atan2(direction[1], direction[0])
            target_speed = self.max_speed * 0.8  # 搜索时保持80%最大速度
            
            return target_heading, target_speed
        return None, None

class Drone(Entity):
    def __init__(self, pos, vel=0.02, heading=0):
        # super().__init__(pos, vel, heading, min_turn_radius=0.1, max_speed=0.15, min_speed=0.02)
        super().__init__(pos, vel, heading, min_turn_radius=0.1, max_speed=0.15, min_speed=0.0)
        self.frozen_time = 0
        self.detect_angle = math.radians(60)  # 60度扇形
        self.detect_range = 60  # 假设
        self.energy = 1.0  # 满能量
        self.max_air_time = 7200
        self.charge_time = 18000
        self.charging = False
        self.on_boat = None
        self.active = True
        self.search_altitude_pattern = 0  # 搜索高度模式
        self.cooperative_target = None    # 协同目标

        # 跟踪状态管理
        self.tracking_target = None
        self.tracking_mode = False
        self.ideal_tracking_distance = 35.0  # 理想跟踪距离（千米，实际35米）
        self.min_tracking_distance = 25.0    # 最小跟踪距离
        self.max_tracking_distance = 45.0    # 最大跟踪距离
        self.tracking_lost_time = 0          # 失去目标的时间
        self.max_tracking_lost_time = 500    # 最大失去目标时间（5秒）

        # 增强跟踪算法
        self.target_velocity_history = []    # 目标速度历史
        self.target_position_history = []    # 目标位置历史
        self.predicted_target_pos = None     # 预测目标位置
        self.tracking_confidence = 1.0       # 跟踪置信度
        self.intercept_mode = False          # 拦截模式
        self.optimal_tracking_angle = 0      # 最优跟踪角度

        # 增强搜索策略
        self.search_mode = 'initial_sweep'   # 搜索模式：initial_sweep, area_patrol, target_hunt
        self.assigned_search_zone = None     # 分配的搜索区域
        self.search_waypoints = []           # 搜索路径点
        self.current_waypoint_index = 0      # 当前路径点索引
        self.search_pattern_type = 'spiral'  # 搜索模式：spiral, zigzag, expanding_square
        self.last_detection_pos = None       # 最后发现目标的位置
        self.search_priority_areas = []      # 优先搜索区域
        self.coverage_map = {}               # 区域覆盖记录
        self.paired_boat = None              # 配对的无人艇
        self.zone_id = None                  # 分配的区域ID
        self.initial_search_target = None    # 初始搜索目标位置
        self.reached_assigned_zone = False   # 是否已到达分配区域
        self.patrol_waypoints = []           # 巡逻路径点
        self.current_patrol_index = 0        # 当前巡逻点索引
        self.patrol_mode = False             # 是否在巡逻模式

        # 支援模式相关
        self.support_mode = False            # 是否在支援模式
        self.support_target_drone = None     # 支援的目标无人机（用于协调）
        self.support_target_enemy = None     # 直接追踪的敌方目标
        self.support_role = None             # 支援角色：'flanking'(侧翼) 或 'backup'(后备)
        self.tactical_distance = 30.0       # 与敌方保持的战术距离（25-35km）
        self.enemy_position_history = []    # 敌方位置历史（用于预测）
        self.support_intercept_point = None # 支援拦截点

    def start_tracking(self, target):
        """开始跟踪目标"""
        self.tracking_target = target
        self.tracking_mode = True
        self.tracking_lost_time = 0
        self.target_velocity_history = []
        self.target_position_history = []
        self.tracking_confidence = 1.0
        self.intercept_mode = False

    def stop_tracking(self):
        """停止跟踪"""
        self.tracking_target = None
        self.tracking_mode = False
        self.tracking_lost_time = 0
        self.target_velocity_history = []
        self.target_position_history = []
        self.predicted_target_pos = None
        self.tracking_confidence = 1.0
        self.intercept_mode = False

    def update_tracking(self, dt, detected_targets):
        """更新跟踪状态和行为 - 增强版本"""
        if not self.tracking_mode or not self.tracking_target:
            return None, None

        # 检查目标是否仍在探测范围内
        target_in_range = self.tracking_target in detected_targets

        if target_in_range:
            self.tracking_lost_time = 0
            self.tracking_confidence = min(1.0, self.tracking_confidence + 0.1)

            # 更新目标历史信息
            self._update_target_history(dt)

            # 预测目标未来位置
            self._predict_target_position(dt)

            # 计算最优跟踪策略
            return self._calculate_optimal_tracking_behavior(dt)

        else:
            # 目标不在探测范围内
            self.tracking_lost_time += dt
            self.tracking_confidence = max(0.0, self.tracking_confidence - 0.2)

            if self.tracking_lost_time > self.max_tracking_lost_time:
                self.stop_tracking()
                return None, None

            # 使用预测位置继续搜索
            if self.predicted_target_pos is not None:
                search_vec = self.predicted_target_pos - self.pos
                target_heading = math.atan2(search_vec[1], search_vec[0])
                return target_heading, self.max_speed * 0.9

            # 继续朝最后已知方向搜索
            if hasattr(self.tracking_target, 'pos'):
                search_vec = self.tracking_target.pos - self.pos
                target_heading = math.atan2(search_vec[1], search_vec[0])
                return target_heading, self.max_speed * 0.8

        return None, None

    def _update_target_history(self, dt):
        """更新目标历史信息"""
        current_pos = self.tracking_target.pos.copy()
        current_vel = np.array([self.tracking_target.vel * math.cos(self.tracking_target.heading),
                               self.tracking_target.vel * math.sin(self.tracking_target.heading)])

        # 添加到历史记录
        self.target_position_history.append(current_pos)
        self.target_velocity_history.append(current_vel)

        # 保持历史记录长度
        max_history = 10
        if len(self.target_position_history) > max_history:
            self.target_position_history.pop(0)
            self.target_velocity_history.pop(0)

    def _predict_target_position(self, dt):
        """预测目标未来位置"""
        if len(self.target_position_history) < 2:
            self.predicted_target_pos = self.tracking_target.pos.copy()
            return

        # 使用线性预测
        recent_positions = self.target_position_history[-3:]
        if len(recent_positions) >= 2:
            # 计算平均速度
            avg_velocity = np.zeros(2)
            for i in range(1, len(recent_positions)):
                velocity = (recent_positions[i] - recent_positions[i-1]) / dt
                avg_velocity += velocity
            avg_velocity /= (len(recent_positions) - 1)

            # 预测未来3秒的位置
            prediction_time = 300  # 3秒
            self.predicted_target_pos = self.tracking_target.pos + avg_velocity * prediction_time
        else:
            self.predicted_target_pos = self.tracking_target.pos.copy()

    def _calculate_optimal_tracking_behavior(self, dt):
        """计算最优跟踪行为"""
        target_vec = self.tracking_target.pos - self.pos
        distance = np.linalg.norm(target_vec)

        if distance == 0:
            return None, None

        # 计算目标运动方向
        target_direction = np.array([math.cos(self.tracking_target.heading),
                                   math.sin(self.tracking_target.heading)])

        # 根据目标速度和方向选择跟踪策略
        target_speed_magnitude = self.tracking_target.vel

        if target_speed_magnitude > 0.008:  # 目标高速移动
            # 采用拦截跟踪策略
            return self._intercept_tracking_strategy(target_vec, distance, target_direction)
        else:
            # 采用环绕跟踪策略
            return self._orbit_tracking_strategy(target_vec, distance, target_direction)

    def _intercept_tracking_strategy(self, target_vec, distance, target_direction):
        """拦截跟踪策略"""
        self.intercept_mode = True

        # 计算拦截点
        if self.predicted_target_pos is not None:
            intercept_vec = self.predicted_target_pos - self.pos
            intercept_distance = np.linalg.norm(intercept_vec)

            if intercept_distance > 0:
                # 朝拦截点移动
                target_heading = math.atan2(intercept_vec[1], intercept_vec[0])

                # 根据距离调整速度
                if distance > self.max_tracking_distance:
                    target_speed = self.max_speed
                elif distance < self.min_tracking_distance:
                    target_speed = self.min_speed
                else:
                    # 保持相对位置
                    target_speed = self.tracking_target.vel * 1.1  # 略快于目标

                return target_heading, np.clip(target_speed, self.min_speed, self.max_speed)

        # 备用策略：直接追踪
        target_heading = math.atan2(target_vec[1], target_vec[0])
        return target_heading, self.max_speed * 0.9

    def _orbit_tracking_strategy(self, target_vec, distance, target_direction):
        """环绕跟踪策略"""
        self.intercept_mode = False

        # 计算环绕位置
        perpendicular = np.array([-target_direction[1], target_direction[0]])  # 垂直方向

        # 选择环绕方向（左侧或右侧）
        if not hasattr(self, 'orbit_side'):
            self.orbit_side = 1 if random.random() > 0.5 else -1

        # 计算理想环绕位置
        orbit_distance = self.ideal_tracking_distance
        ideal_orbit_pos = self.tracking_target.pos + perpendicular * self.orbit_side * orbit_distance

        # 朝理想位置移动
        to_orbit_vec = ideal_orbit_pos - self.pos
        orbit_distance_current = np.linalg.norm(to_orbit_vec)

        if orbit_distance_current > 0:
            target_heading = math.atan2(to_orbit_vec[1], to_orbit_vec[0])

            # 速度控制
            if distance > self.max_tracking_distance:
                target_speed = self.max_speed * 0.8
            elif distance < self.min_tracking_distance:
                target_speed = self.min_speed
            else:
                target_speed = self.tracking_target.vel * 0.9  # 略慢于目标保持跟踪

            return target_heading, np.clip(target_speed, self.min_speed, self.max_speed)

        return None, None

    def initialize_search_zone(self, zone_bounds, search_pattern='spiral'):
        """初始化搜索区域和路径"""
        self.assigned_search_zone = zone_bounds
        self.search_pattern_type = search_pattern
        self.search_waypoints = self._generate_search_waypoints(zone_bounds, search_pattern)
        self.current_waypoint_index = 0

    def _generate_search_waypoints(self, zone_bounds, pattern='spiral'):
        """生成搜索路径点"""
        waypoints = []
        center_x = (zone_bounds['left'] + zone_bounds['right']) / 2
        center_y = (zone_bounds['bottom'] + zone_bounds['top']) / 2
        width = zone_bounds['right'] - zone_bounds['left']
        height = zone_bounds['top'] - zone_bounds['bottom']

        if pattern == 'spiral':
            # 螺旋搜索模式
            num_turns = 8
            max_radius = min(width, height) / 2 * 0.8
            for i in range(num_turns * 10):
                angle = i * 0.5
                radius = (i / (num_turns * 10)) * max_radius
                x = center_x + radius * math.cos(angle)
                y = center_y + radius * math.sin(angle)
                # 确保点在区域内
                x = max(zone_bounds['left'], min(zone_bounds['right'], x))
                y = max(zone_bounds['bottom'], min(zone_bounds['top'], y))
                waypoints.append(np.array([x, y]))

        elif pattern == 'zigzag':
            # 之字形搜索模式
            num_lines = 6
            for i in range(num_lines):
                y = zone_bounds['bottom'] + (i / (num_lines - 1)) * height
                if i % 2 == 0:
                    # 从左到右
                    for j in range(8):
                        x = zone_bounds['left'] + (j / 7) * width
                        waypoints.append(np.array([x, y]))
                else:
                    # 从右到左
                    for j in range(8):
                        x = zone_bounds['right'] - (j / 7) * width
                        waypoints.append(np.array([x, y]))

        elif pattern == 'expanding_square':
            # 扩展方形搜索
            square_sizes = [0.2, 0.4, 0.6, 0.8]
            for size in square_sizes:
                side_length = min(width, height) * size
                corners = [
                    np.array([center_x - side_length/2, center_y - side_length/2]),
                    np.array([center_x + side_length/2, center_y - side_length/2]),
                    np.array([center_x + side_length/2, center_y + side_length/2]),
                    np.array([center_x - side_length/2, center_y + side_length/2])
                ]
                waypoints.extend(corners)

        return waypoints

    def update_intelligent_search(self, dt, detected_targets, all_black_boats):
        """智能搜索更新 - 支援模式优先于所有其他模式"""
        # 最高优先级：支援模式（立即停止巡逻，切换到支援）
        if self.support_mode:
            return self._execute_support_mission()

        # 第二优先级：跟踪模式
        if self.tracking_mode:
            return None, None  # 跟踪模式下不执行搜索

        # 第三优先级：检查是否发现新目标
        if detected_targets:
            # 发现目标，切换到跟踪模式
            closest_target = min(detected_targets, key=lambda t: np.linalg.norm(self.pos - t.pos))
            self.start_tracking(closest_target)
            self.last_detection_pos = closest_target.pos.copy()
            return None, None

        # 第四优先级：确保无人机前往其分配的区域
        if not self.reached_assigned_zone and self.initial_search_target is not None:
            return self._move_to_assigned_zone()

        # 最低优先级：执行巡逻路径
        if self.patrol_waypoints and len(self.patrol_waypoints) > 0:
            return self._execute_patrol_path()

        # 备用搜索策略
        if self.search_mode == 'initial_sweep':
            return self._execute_waypoint_search(dt)
        elif self.search_mode == 'area_patrol':
            return self._execute_area_patrol(dt)
        elif self.search_mode == 'target_hunt':
            return self._execute_target_hunt(dt, all_black_boats)

        return None, None

    def _execute_support_mission(self):
        """执行支援任务 - 直接朝向敌方船只并保持安全距离"""
        if not self.support_target_enemy or not self.support_target_enemy.active:
            # 敌方目标无效，退出支援模式
            self.exit_support_mode()
            return None, None

        # 获取敌方目标当前位置
        enemy_pos = self.support_target_enemy.pos

        # 计算我方到敌方的方向向量
        direction_to_enemy = enemy_pos - self.pos
        distance_to_enemy = np.linalg.norm(direction_to_enemy)

        # 避免除零错误
        if distance_to_enemy < 0.1:
            return None, None

        # 归一化方向向量
        direction_normalized = direction_to_enemy / distance_to_enemy

        # 执行简化的直接支援
        return self._execute_direct_support(enemy_pos, direction_normalized, distance_to_enemy)

    def _execute_direct_support(self, enemy_pos, direction_normalized, distance_to_enemy):
        """执行直接支援 - 朝向敌艇并保持安全距离"""

        # 设定安全距离范围（25-35km）
        min_safe_distance = 25.0
        max_safe_distance = 35.0
        optimal_distance = 30.0  # 最优距离

        # 根据当前距离决定行为
        if distance_to_enemy > max_safe_distance:
            # 距离太远，直接朝敌方高速接近
            target_heading = math.atan2(direction_normalized[1], direction_normalized[0])
            target_speed = self.max_speed * 0.9  # 高速接近

        elif distance_to_enemy < min_safe_distance:
            # 距离太近，需要后退到安全距离
            # 计算远离敌方的方向
            retreat_direction = -direction_normalized
            target_heading = math.atan2(retreat_direction[1], retreat_direction[0])
            target_speed = self.max_speed * 0.7  # 中速后退

        else:
            # 在安全距离内，始终朝向敌方目标
            # 支援无人机应该始终面向敌艇，而不是跟随敌方移动方向
            target_heading = math.atan2(direction_normalized[1], direction_normalized[0])

            # 获取敌方速度向量用于跟随移动
            enemy_velocity = np.array([
                self.support_target_enemy.vel * math.cos(self.support_target_enemy.heading),
                self.support_target_enemy.vel * math.sin(self.support_target_enemy.heading)
            ])

            if np.linalg.norm(enemy_velocity) > 0.001:
                # 敌方在移动，跟随其速度但保持朝向敌方
                target_speed = min(self.max_speed * 0.8, np.linalg.norm(enemy_velocity) * 1.1)
            else:
                # 敌方静止，微调位置以保持最优距离
                if distance_to_enemy > optimal_distance:
                    # 稍微接近
                    target_speed = self.max_speed * 0.3
                elif distance_to_enemy < optimal_distance:
                    # 稍微远离
                    target_speed = self.max_speed * 0.3
                else:
                    # 距离合适，保持位置
                    target_speed = self.max_speed * 0.1

        return target_heading, target_speed



    def enter_support_mode(self, target_drone, target_enemy, role):
        """进入支援模式 - 立即停止巡逻，切换到支援"""
        # 立即停止当前巡逻行为
        self.patrol_mode = False

        # 进入支援模式
        self.support_mode = True
        self.support_target_drone = target_drone  # 用于协调
        self.support_target_enemy = target_enemy  # 直接追踪目标
        self.support_role = role  # 保留角色信息但不影响行为

        # 设置统一的战术距离（25-35km安全距离）
        self.tactical_distance = 30.0  # 统一使用30km作为最优距离

        # 清空位置历史，重新开始记录
        self.enemy_position_history = []
        self.support_intercept_point = None

    def exit_support_mode(self):
        """退出支援模式 - 恢复巡逻状态"""
        self.support_mode = False
        self.support_target_drone = None
        self.support_target_enemy = None
        self.support_role = None

        # 清理支援相关数据
        self.enemy_position_history = []
        self.support_intercept_point = None
        self.tactical_distance = 30.0  # 重置为默认值

        # 恢复巡逻模式（如果有巡逻点）
        if self.patrol_waypoints and len(self.patrol_waypoints) > 0:
            self.patrol_mode = True

    def _execute_patrol_path(self):
        """执行固定巡逻路径 - 优化的流畅巡逻转换"""
        if not self.patrol_waypoints or len(self.patrol_waypoints) == 0:
            return None, None

        # 确保索引有效
        if not hasattr(self, 'current_patrol_index'):
            self.current_patrol_index = 0
        if self.current_patrol_index >= len(self.patrol_waypoints):
            self.current_patrol_index = 0

        # 获取当前目标巡逻点
        current_target = self.patrol_waypoints[self.current_patrol_index]
        direction_to_target = current_target - self.pos
        distance_to_target = np.linalg.norm(direction_to_target)

        # 优化的巡逻点转换逻辑 - 更流畅的转换
        transition_threshold = 6.0  # 6km转换距离，更流畅的巡逻
        if distance_to_target < transition_threshold:
            # 在合理距离内就转换到下一个巡逻点，无需精确到达
            self.current_patrol_index = (self.current_patrol_index + 1) % len(self.patrol_waypoints)

            # 更新目标到新的巡逻点
            current_target = self.patrol_waypoints[self.current_patrol_index]
            direction_to_target = current_target - self.pos
            distance_to_target = np.linalg.norm(direction_to_target)

        # 计算朝向目标的方向和速度
        if distance_to_target > 0.1:  # 避免除零
            target_heading = math.atan2(direction_to_target[1], direction_to_target[0])

            # 优化的速度控制 - 保持更一致的巡逻速度
            if distance_to_target > 15.0:
                target_speed = self.max_speed * 0.95  # 远距离高速
            elif distance_to_target > 8.0:
                target_speed = self.max_speed * 0.8   # 中距离较高速
            else:
                target_speed = self.max_speed * 0.7   # 近距离中速，保持流畅

            return target_heading, target_speed

        return None, None

    def _move_to_assigned_zone(self):
        """移动到分配的搜索区域"""
        if self.initial_search_target is None:
            return None, None

        # 计算到分配区域中心的距离
        direction_to_zone = self.initial_search_target - self.pos
        distance_to_zone = np.linalg.norm(direction_to_zone)

        # 如果已经接近分配区域，标记为已到达
        if distance_to_zone < 5.0:  # 5km容差
            self.reached_assigned_zone = True
            self.search_mode = 'initial_sweep'  # 切换到正式搜索模式
            return self._execute_waypoint_search(0)  # 开始区域内搜索

        # 朝分配区域移动
        target_heading = math.atan2(direction_to_zone[1], direction_to_zone[0])
        target_speed = self.max_speed * 0.9  # 高速前往分配区域

        return target_heading, target_speed

    def _execute_waypoint_search(self, dt):
        """执行路径点搜索"""
        if not self.search_waypoints:
            return None, None

        current_target = self.search_waypoints[self.current_waypoint_index]
        distance_to_target = np.linalg.norm(self.pos - current_target)

        # 到达当前路径点，移动到下一个
        if distance_to_target < 3.0:  # 3km容差
            self.current_waypoint_index = (self.current_waypoint_index + 1) % len(self.search_waypoints)
            if self.current_waypoint_index == 0:
                # 完成一轮搜索，切换到区域巡逻模式
                self.search_mode = 'area_patrol'
            current_target = self.search_waypoints[self.current_waypoint_index]

        # 计算朝向目标的方向
        direction = current_target - self.pos
        target_heading = math.atan2(direction[1], direction[0])
        target_speed = self.max_speed * 0.9  # 高速搜索

        return target_heading, target_speed

    def _execute_area_patrol(self, dt):
        """执行区域巡逻"""
        if not self.assigned_search_zone:
            return None, None

        # 在分配区域内进行随机巡逻
        center_x = (self.assigned_search_zone['left'] + self.assigned_search_zone['right']) / 2
        center_y = (self.assigned_search_zone['bottom'] + self.assigned_search_zone['top']) / 2

        # 添加一些随机性避免过于规律
        patrol_radius = 20.0
        angle = self.search_altitude_pattern * 0.02
        target_x = center_x + patrol_radius * math.cos(angle)
        target_y = center_y + patrol_radius * math.sin(angle)

        direction = np.array([target_x, target_y]) - self.pos
        target_heading = math.atan2(direction[1], direction[0])
        target_speed = self.max_speed * 0.7  # 中速巡逻

        self.search_altitude_pattern += 1
        return target_heading, target_speed

    def _execute_target_hunt(self, dt, all_black_boats):
        """执行目标搜寻"""
        if self.last_detection_pos is not None:
            # 朝最后发现目标的位置搜索
            direction = self.last_detection_pos - self.pos
            distance = np.linalg.norm(direction)

            if distance > 2.0:
                target_heading = math.atan2(direction[1], direction[0])
                target_speed = self.max_speed
                return target_heading, target_speed
            else:
                # 到达最后位置，切换回区域巡逻
                self.search_mode = 'area_patrol'
                self.last_detection_pos = None

        return self._execute_area_patrol(dt)

    def detect(self, targets: List['Boat']) -> List['Boat']:
        # 前方60度扇形探测
        detected = []
        for t in targets:
            vec = t.pos - self.pos
            dist = np.linalg.norm(vec)
            if dist > self.detect_range:
                continue
            angle = math.atan2(vec[1], vec[0]) - self.heading
            angle = (angle + math.pi) % (2 * math.pi) - math.pi
            if abs(angle) < self.detect_angle / 2:
                detected.append(t)
        return detected

    def update_energy(self, dt):
        if self.charging and self.on_boat.frozen_time > 0:
            return  # 暂停补能
        if self.charging:
            self.energy = min(1.0, self.energy + dt/self.charge_time)
        else:
            # self.energy = max(0, self.energy - dt/self.max_air_time)
            self.energy = 1

    def update_cooperative_search(self, dt, mother_boat):
        """更新协同搜索行为"""
        if self.on_boat:
            return None, None
            
        # 与母船协同搜索：无人机在母船前方扇形搜索
        if mother_boat and mother_boat.assigned_zone:
            # 计算母船前方的搜索位置
            boat_direction = np.array([math.cos(mother_boat.heading), math.sin(mother_boat.heading)])
            search_distance = 30.0  # 在母船前方30km处搜索
            
            # 添加左右摆动搜索模式
            swing_angle = math.sin(self.search_altitude_pattern * 0.01) * math.pi / 6  # ±30度摆动
            search_heading = mother_boat.heading + swing_angle
            
            search_pos = mother_boat.pos + boat_direction * search_distance
            search_pos += np.array([math.cos(search_heading), math.sin(search_heading)]) * 10
            
            # 朝向搜索位置
            direction = search_pos - self.pos
            if np.linalg.norm(direction) > 1.0:
                target_heading = math.atan2(direction[1], direction[0])
                target_speed = self.max_speed * 0.9
                
                self.search_altitude_pattern += 1
                return target_heading, target_speed
                
        return None, None

    def should_return_for_charging(self, white_boats=None):
        """智能判断是否需要返回充电"""
        if white_boats is None:
            white_boats = []

        # 基础能量阈值
        base_energy_threshold = 0.3

        # 根据任务紧急程度调整阈值
        if self.tracking_mode and self.tracking_target:
            # 正在跟踪目标时，降低返回阈值
            if self.tracking_confidence > 0.8:
                base_energy_threshold = 0.15  # 高置信度跟踪时延迟返回
            else:
                base_energy_threshold = 0.25  # 低置信度时提前返回

        # 考虑返回距离的动态阈值
        if white_boats:
            nearest_boat = min([b for b in white_boats if b.active],
                             key=lambda b: np.linalg.norm(self.pos - b.pos))
            return_distance = np.linalg.norm(self.pos - nearest_boat.pos)

            # 计算返回所需能量
            estimated_return_time = return_distance / self.max_speed
            estimated_return_energy = estimated_return_time / self.max_air_time

            # 动态调整阈值
            safety_margin = 1.8  # 安全系数
            required_energy = estimated_return_energy * safety_margin

            # 如果当前能量不足以安全返回，立即返回
            if self.energy < required_energy:
                return True

            # 如果能量低于基础阈值，返回
            if self.energy < base_energy_threshold:
                return True
        else:
            # 没有可用母船信息时，使用保守策略
            if self.energy < 0.4:
                return True

        return False

    def get_energy_management_strategy(self, mission_priority='normal'):
        """获取能量管理策略"""
        strategies = {
            'conservative': {
                'return_threshold': 0.4,
                'tracking_threshold': 0.2,
                'search_speed_factor': 0.7
            },
            'normal': {
                'return_threshold': 0.3,
                'tracking_threshold': 0.15,
                'search_speed_factor': 0.8
            },
            'aggressive': {
                'return_threshold': 0.2,
                'tracking_threshold': 0.1,
                'search_speed_factor': 0.9
            }
        }

        return strategies.get(mission_priority, strategies['normal'])

    def optimize_flight_pattern_for_energy(self, target_heading, target_speed):
        """优化飞行模式以节省能量"""
        # 根据能量水平调整速度
        if self.energy < 0.5:
            # 能量较低时，降低速度以延长飞行时间
            energy_factor = self.energy * 2  # 0.5以下时线性降低
            target_speed *= energy_factor

        # 避免频繁转向以节省能量
        heading_diff = abs(target_heading - self.heading)
        if heading_diff > math.pi:
            heading_diff = 2 * math.pi - heading_diff

        if heading_diff > math.pi / 4 and self.energy < 0.3:
            # 能量低且需要大幅转向时，采用更平缓的转向
            if target_heading > self.heading:
                target_heading = self.heading + math.pi / 6
            else:
                target_heading = self.heading - math.pi / 6

        return target_heading, max(target_speed, self.min_speed)
    
    def get_optimal_charging_schedule(self, mission_duration):
        """获取最优充电计划"""
        # 基于任务持续时间和搜索区域大小计算充电计划
        cycles_needed = mission_duration / self.max_air_time
        return {
            'charge_intervals': self.charge_time,
            'flight_intervals': self.max_air_time * 0.8,  # 保留20%安全余量
            'total_cycles': int(cycles_needed) + 1
        }
