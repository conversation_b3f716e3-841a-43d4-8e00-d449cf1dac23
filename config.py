import numpy as np

class Config:
    # 常量定义（基于文档，单位统一为米，时间单位为秒）
    # KM_TO_M = 1000
    # TASK_AREA_SCALE = 0.001  # 渲染缩放因子，因为区域太大，Pygame窗口有限
    SQRT_3 = 1.732050807
    SCREEN_WIDTH = 800
    SCREEN_HEIGHT = 600
    WHITE = (255, 255, 255)
    BLACK = (0, 0, 0)
    RED = (255, 0, 0)
    BLUE = (0, 0, 255)
    GREEN = (0, 255, 0)
    LIGHT_BLUE = (200, 200, 255)
    YELLOW = (255, 255, 0)

    # 区域点坐标（假设A7A8为底部，A1A6为顶部，根据描述推断坐标，单位米）
    # 简化坐标系：原点在A7A8中点下方
    AREA_POINTS = {
        'A1': np.array([ -250-50*SQRT_3, 50 ]),  # 基于距离计算的近似位置
        'A2': np.array([ -250-50*SQRT_3, 250 ]),
        'A3': np.array([ -250, 300 ]),
        'A4': np.array([ 250, 300 ]),
        'A5': np.array([ 250+50*SQRT_3, 250 ]),
        'A6': np.array([ 250+50*SQRT_3, 50 ]),
        'A7': np.array([ 250, 0 ]),
        'A8': np.array([ -250, 0 ]),
        # 注意：以上坐标是基于距离的近似，需要精确计算多边形，但为简化假设
    }

    # A1A6红线：从A1到A6
    BREAKTHROUGH_LINE = [AREA_POINTS['A1'], AREA_POINTS['A6']]

    # A7A8线中点
    A7A8_MID = (AREA_POINTS['A7'] + AREA_POINTS['A8']) / 2

    # A2A3A4A5梯形
    BLACK_INIT_AREA = [AREA_POINTS['A2'], AREA_POINTS['A3'], AREA_POINTS['A4'], AREA_POINTS['A5']]

    ALL_AREA = [AREA_POINTS['A1'], AREA_POINTS['A2'], AREA_POINTS['A3'], AREA_POINTS['A4'], AREA_POINTS['A5'], AREA_POINTS['A6'], AREA_POINTS['A7'], AREA_POINTS['A8']]