import gym
from gym import spaces
import numpy as np
import pygame
import math
from config import Config
from entity import Boat, Drone
from typing import List
from matplotlib.path import Path

# 环境类
class UnmannedClusterEnv(gym.Env):
    metadata = {'render.modes': ['human']}

    def __init__(self):
        super(UnmannedClusterEnv, self).__init__()
        self.num_white_boats = 5
        self.num_white_drones = 5
        self.num_black_boats = 2  # 围捕1个黑船

        # 动作空间：为每个白方艇/无人机指定目标速度和朝向（简化）
        # 每个艇/无人机： [target_speed_norm, target_heading]
        action_dim = 2 * (self.num_white_boats + self.num_white_drones)
        self.action_space = spaces.Box(low=-1, high=1, shape=(action_dim,), dtype=np.float32)

        # 观测空间：所有实体位置、速度、朝向、锁定状态等（简化）
        obs_dim = 6 * (self.num_white_boats + self.num_white_drones + self.num_black_boats)  # pos_x, pos_y, vel, heading, energy/lock, etc.
        self.observation_space = spaces.Box(low=-np.inf, high=np.inf, shape=(obs_dim,), dtype=np.float32)

        self.white_boats: List[Boat] = []
        self.white_drones: List[Drone] = []
        self.black_boats: List[Boat] = []
        self.dt = 100  # 时间步，秒
        self.time = 0
        self.intercepted = 0
        self.lock_success = 0
        self.be_locked = 0
        self.collisions = 0
        self.total_black_intercepted = 0
        self.total_lock_success = 0
        self.total_be_locked = 0
        self.total_collisions = 0
        self.random_target_pos = []
        # Pygame
        self.screen = None

        self.search_mode = True  # 搜索模式标志
        self.search_zones = []   # 搜索区域划分
        self.zone_assignments = {}  # 区域分配
        self.patrol_waypoints = {}  # 巡逻路径点

        # 跨平台信息共享
        self.shared_target_info = {}  # 共享目标信息
        self.communication_network = {}  # 通信网络
        self.priority_targets = []  # 优先目标列表

        # 无人机支援机制
        self.drone_support_assignments = {}  # 无人机支援分配 {支援者: 被支援者}

        # 固定巡逻点系统
        self.fixed_patrol_points = []  # 存储所有无人机的巡逻点

        self.reset()
        # 初始化搜索策略和巡逻点
        self._initialize_search_strategy()

    def _initialize_search_strategy(self):
        """初始化搜索策略 - 基于A1A2A3A4A5A6六边形区域的固定巡逻点系统"""
        # 使用A1A2A3A4A5A6六边形区域
        hexagon_points = [
            Config.AREA_POINTS['A1'], Config.AREA_POINTS['A2'], Config.AREA_POINTS['A3'],
            Config.AREA_POINTS['A4'], Config.AREA_POINTS['A5'], Config.AREA_POINTS['A6']
        ]

        # 为5个无人机生成固定的巡逻点系统
        self.fixed_patrol_points = self._generate_fixed_patrol_points(hexagon_points)

        for i in range(5):
            # 分配无人艇和无人机到区域（确保一一对应）
            if i < len(self.white_boats) and i < len(self.white_drones):
                # 建立无人艇-无人机配对关系
                boat = self.white_boats[i]
                drone = self.white_drones[i]

                boat.paired_drone = drone  # 建立配对关系
                drone.paired_boat = boat   # 建立配对关系

                # 为无人机分配固定的10个巡逻点
                if i < len(self.fixed_patrol_points):
                    drone.patrol_waypoints = self.fixed_patrol_points[i]
                    drone.current_patrol_index = 0
                    drone.drone_id = i  # 设置无人机ID用于可视化

                    # 设置无人机的初始目标为第一个巡逻点
                    if len(drone.patrol_waypoints) > 0:
                        drone.initial_search_target = drone.patrol_waypoints[0].copy()
                else:
                    drone.patrol_waypoints = []

                # 为无人艇生成简单的跟随区域
                zone = {
                    'id': i,
                    'center': drone.patrol_waypoints[0].copy(),
                    'bounds': self._calculate_patrol_bounds(drone.patrol_waypoints)
                }
                boat.assigned_zone = zone


    def _generate_fixed_patrol_points(self, hexagon_points):
        """为5个无人机生成固定的巡逻点系统，每个无人机10个点"""
        # 计算六边形的边界
        x_coords = [p[0] for p in hexagon_points]
        y_coords = [p[1] for p in hexagon_points]
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        center_x = (x_min + x_max) / 2
        center_y = (y_min + y_max) / 2

        # 计算六边形的有效区域
        width = x_max - x_min
        height = y_max - y_min

        all_patrol_points = []

        # 为每个无人机生成10个巡逻点
        for drone_id in range(5):
            drone_points = []

            if drone_id == 0:  # D1: 左上区域
                base_x = x_min + width * 0.25
                base_y = y_min + height * 0.6
                # 生成矩形巡逻模式
                for i in range(10):
                    angle = 2 * math.pi * i / 10
                    radius = min(width, height) * 0.15
                    x = base_x + radius * math.cos(angle)
                    y = base_y + radius * 0.6 * math.sin(angle)  # 椭圆形
                    drone_points.append(np.array([x, y]))

            elif drone_id == 1:  # D2: 右上区域
                base_x = x_min + width * 0.75
                base_y = y_min + height * 0.6
                # 生成菱形巡逻模式
                diamond_points = [
                    [0, 0.2], [0.15, 0.1], [0.2, 0], [0.15, -0.1], [0, -0.2],
                    [-0.15, -0.1], [-0.2, 0], [-0.15, 0.1], [0, 0.15], [0.1, 0.05]
                ]
                for i, (dx, dy) in enumerate(diamond_points):
                    x = base_x + dx * width
                    y = base_y + dy * height
                    drone_points.append(np.array([x, y]))

            elif drone_id == 2:  # D3: 中央区域
                base_x = center_x
                base_y = center_y
                # 生成螺旋巡逻模式
                for i in range(10):
                    angle = 2 * math.pi * i / 10 * 1.5  # 1.5圈螺旋
                    radius = min(width, height) * 0.1 * (1 + i / 10)  # 递增半径
                    x = base_x + radius * math.cos(angle)
                    y = base_y + radius * math.sin(angle)
                    drone_points.append(np.array([x, y]))

            elif drone_id == 3:  # D4: 左下区域
                base_x = x_min + width * 0.25
                base_y = y_min + height * 0.3
                # 生成十字形巡逻模式
                cross_points = [
                    [0, 0], [0.15, 0], [0.1, 0.1], [0, 0.15], [-0.1, 0.1],
                    [-0.15, 0], [-0.1, -0.1], [0, -0.15], [0.1, -0.1], [0.05, 0]
                ]
                for i, (dx, dy) in enumerate(cross_points):
                    x = base_x + dx * width
                    y = base_y + dy * height
                    drone_points.append(np.array([x, y]))

            else:  # D5: 右下区域
                base_x = x_min + width * 0.75
                base_y = y_min + height * 0.3
                # 生成八字形巡逻模式
                figure_eight_points = [
                    [0, 0], [0.1, 0.1], [0.15, 0], [0.1, -0.1], [0, 0],
                    [-0.1, 0.1], [-0.15, 0], [-0.1, -0.1], [0, 0], [0.05, 0.05]
                ]
                for i, (dx, dy) in enumerate(figure_eight_points):
                    x = base_x + dx * width
                    y = base_y + dy * height
                    drone_points.append(np.array([x, y]))

            # 确保所有点都在六边形内
            validated_points = []
            for point in drone_points:
                if self._point_in_hexagon(point, hexagon_points):
                    validated_points.append(point)
                else:
                    # 如果点在六边形外，将其投影到六边形内
                    projected_point = self._project_point_to_hexagon(point, hexagon_points)
                    validated_points.append(projected_point)

            all_patrol_points.append(validated_points)

        return all_patrol_points

    def _point_in_hexagon(self, point, hexagon_points):
        """检查点是否在六边形内"""
        # 使用射线法检测点是否在多边形内
        x, y = point[0], point[1]
        n = len(hexagon_points)
        inside = False

        p1x, p1y = hexagon_points[0][0], hexagon_points[0][1]
        for i in range(1, n + 1):
            p2x, p2y = hexagon_points[i % n][0], hexagon_points[i % n][1]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def _project_point_to_hexagon(self, point, hexagon_points):
        """将点投影到六边形内"""
        # 简单的投影方法：找到最近的六边形边界点
        min_dist = float('inf')
        closest_point = point.copy()

        # 计算六边形中心
        center = np.mean(hexagon_points, axis=0)

        # 将点朝六边形中心移动
        direction = center - point
        distance = np.linalg.norm(direction)
        if distance > 0:
            # 移动到六边形中心的80%位置
            closest_point = point + direction * 0.8

        return closest_point

    def _calculate_patrol_bounds(self, patrol_waypoints):
        """计算巡逻点的边界"""
        if not patrol_waypoints:
            return {'left': 0, 'right': 0, 'bottom': 0, 'top': 0}

        x_coords = [p[0] for p in patrol_waypoints]
        y_coords = [p[1] for p in patrol_waypoints]

        return {
            'left': min(x_coords) - 10,
            'right': max(x_coords) + 10,
            'bottom': min(y_coords) - 10,
            'top': max(y_coords) + 10
        }

    def _generate_optimized_patrol_zones(self, all_area_points, x_min, x_max, y_min, y_max):
        """生成优化的巡逻区域，覆盖整个八边形"""
        zones = []

        # 将八边形区域分为5个重叠的扇形/区域，确保全覆盖
        center_x = (x_min + x_max) / 2
        center_y = (y_min + y_max) / 2

        # 定义5个巡逻区域的中心点，均匀分布在八边形内
        zone_centers = [
            np.array([x_min + 0.2 * (x_max - x_min), y_min + 0.3 * (y_max - y_min)]),  # 左下
            np.array([x_min + 0.8 * (x_max - x_min), y_min + 0.3 * (y_max - y_min)]),  # 右下
            np.array([center_x, center_y]),                                              # 中央
            np.array([x_min + 0.2 * (x_max - x_min), y_min + 0.7 * (y_max - y_min)]),  # 左上
            np.array([x_min + 0.8 * (x_max - x_min), y_min + 0.7 * (y_max - y_min)])   # 右上
        ]

        # 为每个区域定义边界
        zone_radius = min(x_max - x_min, y_max - y_min) * 0.4  # 重叠区域半径

        for i, center in enumerate(zone_centers):
            zone = {
                'id': i,
                'center': center,
                'bounds': {
                    'left': max(x_min, center[0] - zone_radius),
                    'right': min(x_max, center[0] + zone_radius),
                    'bottom': max(y_min, center[1] - zone_radius),
                    'top': min(y_max, center[1] + zone_radius)
                },
                'radius': zone_radius
            }
            zones.append(zone)

        return zones



    def _handle_cross_platform_detection(self):
        """处理跨平台探测和信息共享"""
        # 清空之前的共享信息
        self.shared_target_info = {}

        # 收集所有探测信息
        for drone in self.white_drones:
            if not drone.active or drone.on_boat:
                continue

            detected_targets = drone.detect(self.black_boats)
            for target in detected_targets:
                target_id = id(target)
                if target_id not in self.shared_target_info:
                    self.shared_target_info[target_id] = {
                        'target': target,
                        'detected_by': [],
                        'last_seen_pos': target.pos.copy(),
                        'last_seen_time': self.time,
                        'priority': self._calculate_target_priority(target)
                    }

                self.shared_target_info[target_id]['detected_by'].append(drone)
                self.shared_target_info[target_id]['last_seen_pos'] = target.pos.copy()
                self.shared_target_info[target_id]['last_seen_time'] = self.time

        # 更新优先目标列表
        self._update_priority_targets()

        # 分配无人机支援任务
        self._assign_drone_support()

    def _calculate_target_priority(self, target):
        """计算目标优先级"""
        # 基于距离突防线的距离计算优先级
        breakthrough_center = (Config.BREAKTHROUGH_LINE[0] + Config.BREAKTHROUGH_LINE[1]) / 2
        distance_to_breakthrough = np.linalg.norm(target.pos - breakthrough_center)

        # 距离越近，优先级越高
        base_priority = 1.0 / (distance_to_breakthrough + 1.0)

        # 考虑目标速度
        speed_factor = target.vel * 10  # 速度越快优先级越高

        # 考虑目标朝向（是否朝向突防线）
        direction_to_breakthrough = breakthrough_center - target.pos
        target_direction = np.array([math.cos(target.heading), math.sin(target.heading)])
        direction_alignment = np.dot(direction_to_breakthrough / np.linalg.norm(direction_to_breakthrough),
                                   target_direction)
        direction_factor = max(0, direction_alignment)  # 朝向突防线的目标优先级更高

        total_priority = base_priority + speed_factor + direction_factor
        return total_priority

    def _update_priority_targets(self):
        """更新优先目标列表"""
        # 按优先级排序
        sorted_targets = sorted(self.shared_target_info.items(),
                              key=lambda x: x[1]['priority'], reverse=True)

        self.priority_targets = [target_info for _, target_info in sorted_targets[:3]]  # 取前3个优先目标



    def _update_search_mode(self):
        """更新搜索模式"""
        # 如果发现目标，部分无人机切换到跟踪模式
        active_tracking_drones = sum(1 for drone in self.white_drones
                                   if drone.active and drone.tracking_mode)

        # 如果没有足够的跟踪无人机，让一些无人机切换到搜索模式
        if active_tracking_drones == 0 and len(self.priority_targets) > 0:
            for drone in self.white_drones:
                if drone.active and not drone.on_boat and not drone.tracking_mode:
                    # 让无人机搜索优先目标区域
                    if self.priority_targets:
                        target_area = self.priority_targets[0]['last_seen_pos']
                        drone.search_mode = 'target_hunt'
                        drone.last_detection_pos = target_area
                    break



    def _select_optimal_target(self, boat, available_targets):
        """为无人艇选择最优目标"""
        best_target = None
        best_score = float('inf')

        for target in available_targets:
            # 计算综合评分
            distance = np.linalg.norm(boat.pos - target.pos)

            # 获取目标优先级
            target_id = id(target)
            priority = 1.0
            if target_id in self.shared_target_info:
                priority = self.shared_target_info[target_id]['priority']

            # 评分：距离越近越好，优先级越高越好
            score = distance / (priority + 0.1)  # 避免除零

            if score < best_score:
                best_score = score
                best_target = target

        return best_target if best_target else available_targets[0]

    def _assign_drone_support(self):
        """分配无人机支援任务：一个无人机探测到敌方后，最近的两个没有探测到敌方的无人机去支援"""
        # 清空之前的支援分配
        self.drone_support_assignments = {}

        # 找到正在跟踪目标的无人机（需要支援的无人机）
        tracking_drones = []
        available_drones = []

        for drone in self.white_drones:
            if not drone.active or drone.on_boat:
                continue

            if drone.tracking_mode and drone.tracking_target:
                tracking_drones.append(drone)
            else:
                available_drones.append(drone)

        # 为每个正在跟踪的无人机分配最近的两个支援无人机
        for tracking_drone in tracking_drones:
            if len(available_drones) == 0:
                break

            # 计算所有可用无人机到跟踪无人机的距离
            drone_distances = []
            for available_drone in available_drones:
                distance = np.linalg.norm(tracking_drone.pos - available_drone.pos)
                drone_distances.append((available_drone, distance))

            # 按距离排序，选择最近的两个
            drone_distances.sort(key=lambda x: x[1])

            # 分配最多两个支援无人机
            support_count = 0
            for support_drone, distance in drone_distances:
                if support_count >= 2:  # 最多两个支援
                    break

                # 检查支援无人机是否已经被分配给其他任务
                if support_drone not in self.drone_support_assignments:
                    self.drone_support_assignments[support_drone] = {
                        'target_drone': tracking_drone,
                        'target_enemy': tracking_drone.tracking_target,
                        'support_role': 'flanking' if support_count == 0 else 'backup',
                        'distance': distance
                    }
                    available_drones.remove(support_drone)
                    support_count += 1

    def _apply_drone_support_assignments(self):
        """应用无人机支援分配 - 确保目标信息共享和优先级处理"""
        # 首先清除无效的支援模式
        for drone in self.white_drones:
            if drone.support_mode and drone not in self.drone_support_assignments:
                drone.exit_support_mode()

        # 应用新的支援分配
        for support_drone, assignment in self.drone_support_assignments.items():
            if support_drone.active and not support_drone.on_boat:
                target_drone = assignment['target_drone']
                target_enemy = assignment['target_enemy']
                support_role = assignment['support_role']

                # 检查敌方目标是否仍然有效（不再依赖主跟踪无人机状态）
                if target_enemy.active:
                    # 确保支援无人机接收到最新的敌方目标信息
                    self._share_enemy_target_info(support_drone, target_enemy)

                    # 进入或更新支援模式（支援优先于巡逻）
                    if not support_drone.support_mode:
                        # 新进入支援模式，立即停止巡逻
                        support_drone.enter_support_mode(target_drone, target_enemy, support_role)
                    else:
                        # 更新支援目标信息
                        support_drone.support_target_enemy = target_enemy
                        support_drone.support_target_drone = target_drone
                else:
                    # 敌方目标无效，退出支援模式，恢复巡逻
                    support_drone.exit_support_mode()

    def _share_enemy_target_info(self, support_drone, target_enemy):
        """向支援无人机共享敌方目标的精确位置和移动数据"""
        # 确保支援无人机有最新的敌方目标信息
        target_id = id(target_enemy)

        # 更新共享目标信息
        if target_id in self.shared_target_info:
            target_info = self.shared_target_info[target_id]
            target_info['last_seen_pos'] = target_enemy.pos.copy()
            target_info['last_seen_time'] = self.time
            target_info['velocity'] = np.array([target_enemy.vel * math.cos(target_enemy.heading),
                                               target_enemy.vel * math.sin(target_enemy.heading)])
            target_info['heading'] = target_enemy.heading
        else:
            # 创建新的目标信息
            self.shared_target_info[target_id] = {
                'target': target_enemy,
                'detected_by': [support_drone],
                'last_seen_pos': target_enemy.pos.copy(),
                'last_seen_time': self.time,
                'priority': self._calculate_target_priority(target_enemy),
                'velocity': np.array([target_enemy.vel * math.cos(target_enemy.heading),
                                     target_enemy.vel * math.sin(target_enemy.heading)]),
                'heading': target_enemy.heading
            }

    def _draw_patrol_points(self, scale_pos):
        """绘制所有无人机的巡逻点"""
        # 定义每个无人机的颜色
        drone_colors = [
            (255, 100, 100),  # D1: 红色
            (100, 255, 100),  # D2: 绿色
            (100, 100, 255),  # D3: 蓝色
            (255, 255, 100),  # D4: 黄色
            (255, 100, 255),  # D5: 紫色
        ]

        if hasattr(self, 'fixed_patrol_points') and self.fixed_patrol_points:
            for drone_id, patrol_points in enumerate(self.fixed_patrol_points):
                if drone_id < len(drone_colors):
                    color = drone_colors[drone_id]

                    # 绘制巡逻点
                    for i, point in enumerate(patrol_points):
                        pos = scale_pos(point)
                        # 绘制巡逻点圆圈
                        pygame.draw.circle(self.screen, color, pos, 4)
                        # 绘制巡逻点编号
                        font = pygame.font.Font(None, 16)
                        text = font.render(f'{i+1}', True, Config.BLACK)
                        self.screen.blit(text, (pos[0] + 6, pos[1] - 6))

                    # 绘制巡逻路径连线
                    if len(patrol_points) > 1:
                        path_points = [scale_pos(p) for p in patrol_points]
                        # 连接所有巡逻点
                        for i in range(len(path_points)):
                            start_pos = path_points[i]
                            end_pos = path_points[(i + 1) % len(path_points)]  # 循环连接
                            pygame.draw.line(self.screen, color, start_pos, end_pos, 1)

                    # 高亮显示当前目标巡逻点
                    if drone_id < len(self.white_drones):
                        drone = self.white_drones[drone_id]
                        if (hasattr(drone, 'current_patrol_index') and
                            hasattr(drone, 'patrol_waypoints') and
                            drone.patrol_waypoints and
                            not drone.tracking_mode):
                            current_index = drone.current_patrol_index
                            if 0 <= current_index < len(patrol_points):
                                current_point = patrol_points[current_index]
                                pos = scale_pos(current_point)
                                # 绘制高亮圆圈
                                pygame.draw.circle(self.screen, (255, 255, 255), pos, 6, 2)
                                pygame.draw.circle(self.screen, color, pos, 6, 2)

    def reset(self):
        self.white_boats = []
        self.white_drones = []
        self.black_boats = []
        self.time = 0
        self.intercepted = 0
        self.lock_success = 0
        self.be_locked = 0
        self.collisions = 0
        self.random_target_pos = []
        # 初始化白方无人艇
        interval = 5
        start_x = Config.A7A8_MID[0] - (self.num_white_boats - 1) * interval / 2
        for i in range(self.num_white_boats):
            pos = np.array([start_x + i * interval, Config.A7A8_MID[1]])
            boat = Boat(pos, heading=math.pi / 2)
            boat.detect_range = 20
            boat.lock_range = 40
            # boat.detect_range = 60
            # boat.lock_range = 60
            boat.side = 'white'
            self.white_boats.append(boat)

        # 初始化白方无人机（初始在对应的艇上）
        for i in range(self.num_white_drones):
            pos = self.white_boats[i].pos.copy()
            drone = Drone(pos, heading=math.pi / 2)
            drone.on_boat = self.white_boats[i]
            drone.charging = True
            # 建立配对关系
            drone.paired_boat = self.white_boats[i]
            self.white_boats[i].paired_drone = drone

            # 设置无人机ID，巡逻点将在_initialize_search_strategy中分配
            drone.zone_id = i

            self.white_drones.append(drone)

        # 初始化黑方无人艇（随机在梯形内，简单随机策略）
        for _ in range(self.num_black_boats):
            # 随机位置在A2A3A4A5（简化均匀随机）
            while True:
                black_area_path = Path(Config.BLACK_INIT_AREA)
                pos = np.random.uniform(low=np.min(Config.BLACK_INIT_AREA, axis=0), high=np.max(Config.BLACK_INIT_AREA, axis=0))
                if black_area_path.contains_point(pos):
                    break
            boat = Boat(pos, vel=0.01, heading=math.pi / 2)
            boat.detect_range = 30
            boat.side = 'black'
            self.black_boats.append(boat)  # 向下

        # 初始化和白色无人艇数量一样多的随机点，保证在地图内
        for _ in range(self.num_white_boats):
            # 随机位置在config的ALL_AREA（简化均匀随机）
            pos = np.random.uniform(low=np.min(Config.ALL_AREA, axis=0), high=np.max(Config.ALL_AREA, axis=0))
            # 判断pos是否在梯形内
            while True:
                all_area_path = Path(Config.ALL_AREA)
                if all_area_path.contains_point(pos):
                    break
                pos = np.random.uniform(low=np.min(Config.ALL_AREA, axis=0), high=np.max(Config.ALL_AREA, axis=0))
            self.random_target_pos.append(pos)
        return self._get_obs()

    def step(self, action):
        # 解析动作
        self.intercepted = 0
        self.lock_success = 0
        self.be_locked = 0
        idx = 0
        for boat in self.white_boats:
            if boat.frozen_time > 0 or not boat.active:
                boat.frozen_time -= self.dt if boat.frozen_time > 0 else 0
                idx += 2
                continue
            target_speed = (action[idx] + 1) / 2 * boat.max_speed
            target_heading = action[idx + 1] * math.pi
            boat.update_position(self.dt, target_heading, target_speed)
            idx += 2

        for drone in self.white_drones:
            if drone.energy <= 0 or not drone.active:
                drone.active = False
                continue
            if drone.on_boat:
                # 起飞决策（简化：如果动作>0起飞）
                if action[idx] > 0 and drone.energy >= 1:
                    drone.on_boat = None
                    drone.charging = False
                    drone.vel = 0.04
                else:
                    drone.pos = drone.on_boat.pos.copy()
                    drone.update_energy(self.dt)
                    idx += 2
                    continue
            else:
                # 降落检查
                for boat in self.white_boats:
                    if np.linalg.norm(drone.pos - boat.pos) < 0.1 and drone.vel < 0.03:
                        if all(d.on_boat != boat for d in self.white_drones if d != drone):
                            drone.on_boat = boat
                            drone.charging = True
                            drone.vel = 0
                            break

            # 无人机智能跟踪逻辑
            detected_targets = drone.detect(self.black_boats)

            # 使用新的智能搜索和跟踪系统
            if drone.tracking_mode:
                # 更新跟踪行为
                tracking_heading, tracking_speed = drone.update_tracking(self.dt, detected_targets)
                if tracking_heading is not None and tracking_speed is not None:
                    target_heading = tracking_heading
                    target_speed = tracking_speed
                else:
                    # 跟踪失败，切换到搜索模式
                    search_heading, search_speed = drone.update_intelligent_search(self.dt, detected_targets, self.black_boats)
                    if search_heading is not None and search_speed is not None:
                        target_heading = search_heading
                        target_speed = search_speed
                    else:
                        target_heading = action[idx + 1] * math.pi
                        target_speed = drone.min_speed + (action[idx] + 1) / 2 * (drone.max_speed - drone.min_speed)
            else:
                # 搜索模式
                search_heading, search_speed = drone.update_intelligent_search(self.dt, detected_targets, self.black_boats)
                if search_heading is not None and search_speed is not None:
                    target_heading = search_heading
                    target_speed = search_speed
                else:
                    # 使用原始动作控制
                    target_speed = drone.min_speed + (action[idx] + 1) / 2 * (drone.max_speed - drone.min_speed)
                    target_heading = action[idx + 1] * math.pi

            # 能量不足时自动返回母船
            if drone.energy < 0.2:  # 能量低于20%时返回
                nearest_boat = min(self.white_boats, key=lambda b: np.linalg.norm(drone.pos - b.pos))
                return_vec = nearest_boat.pos - drone.pos
                target_heading = math.atan2(return_vec[1], return_vec[0])
                target_speed = drone.max_speed

            drone.update_position(self.dt, target_heading, target_speed)
            drone.update_energy(self.dt)
            idx += 2

        # 更新黑方（简单策略：直线向A1A6中点移动）
        target_point = (Config.BREAKTHROUGH_LINE[0] + Config.BREAKTHROUGH_LINE[1]) / 2
        for boat in self.black_boats[:]:
            if boat.frozen_time > 0 or not boat.active:
                boat.frozen_time -= self.dt if boat.frozen_time > 0 else 0
                continue
            direction = target_point - boat.pos
            target_heading = math.atan2(direction[1], direction[0])
            boat.update_position(self.dt, target_heading, boat.max_speed)

            # 检查突防
            if self._cross_line(boat.pos, Config.BREAKTHROUGH_LINE):
                print('通过了！')
                self.black_boats.remove(boat)
                # 未拦截，负奖励

        # 处理跨平台探测和信息共享
        self._handle_cross_platform_detection()

        # 应用无人机支援分配
        self._apply_drone_support_assignments()

        # 探测和锁定（使用协同围捕策略）
        for boat in self.white_boats:
            if boat.exited:
                for drone in self.white_drones[:]:
                    if drone.on_boat == boat:
                        drone.active = False
                boat.active = False
                continue

            if boat.frozen_time > 0:
                continue

            # 常规探测和锁定
            detected = boat.detect(self.black_boats)

            # 添加跨平台探测信息
            for target_info in self.shared_target_info.values():
                if target_info['target'] not in detected:
                    # 检查是否在锁定范围内
                    if np.linalg.norm(boat.pos - target_info['target'].pos) < boat.lock_range:
                        detected.append(target_info['target'])

            if detected:
                can_target = [t for t in detected if t.frozen_time <= 0]
                if can_target:
                    # 优先选择优先级高的目标
                    target = self._select_optimal_target(boat, can_target)

                    # 尝试锁定目标
                    lock_result = boat.lock(target, self.dt)
                    if lock_result:
                        self.lock_success += 1
                        self.total_lock_success += 1
                        if target.lock_count >= 2:
                            self.intercepted += 1
            

        # 黑方锁定白方（简化）
        for b_boat in self.black_boats:
            # print(b_boat.exited)
            if b_boat.exited:
                b_boat.active = False
                self.total_black_intercepted += 1
                continue
            if b_boat.frozen_time > 0:
                continue
            detected = b_boat.detect(self.white_boats)
            if detected:
                can_target = [t for t in detected if t.frozen_time <= 0]
                if can_target:
                    target = min(can_target, key=lambda t: np.linalg.norm(b_boat.pos - t.pos))
                    if b_boat.lock(target, self.dt):
                        self.be_locked += 1
                        self.total_be_locked += 1



        # 碰撞检测
        all_boats = [b for b in self.white_boats + self.black_boats if b.active]
        for i in range(len(all_boats)):
            for j in range(i + 1, len(all_boats)):
                if np.linalg.norm(all_boats[i].pos - all_boats[j].pos) < 0.1:
                    self.collisions += 1
                    self.total_collisions += 1

        # 边界检查：确保所有船只不得出任务区域
        self._enforce_boundary_constraints()

        self.time += self.dt

        obs = self._get_obs()
        reward = self.intercepted * 10 - self.be_locked * 5 - self.collisions * 2 + self.lock_success
        done = all(not b.active for b in self.black_boats) or self.time > 144000  # 1小时
        info = {}
        return obs, reward, done, info
#观测应当加上判断是无人艇还是无人机，是否是友方，无人机是否在艇上等
    def _get_obs(self):
        obs = []
        entities = self.white_boats + self.white_drones + self.black_boats
        for e in entities:
            if not e.active:
                obs.extend([0, 0, 0, 0, 0, 0])  # 假设不观测静止实体
            else:
                obs.extend([e.pos[0], e.pos[1], e.vel, e.heading, 1 if hasattr(e, 'energy') else e.lock_time, 1 if e.frozen_time > 0 else 0])
        return np.array(obs)

    def _is_point_in_polygon(self, point, polygon_vertices):
        """
        使用射线法判断点是否在多边形内部
        """
        x, y = point
        n = len(polygon_vertices)
        inside = False

        p1x, p1y = polygon_vertices[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_vertices[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def _get_closest_point_on_boundary(self, point, polygon_vertices):
        """
        找到多边形边界上距离给定点最近的点
        """
        min_distance = float('inf')
        closest_point = point

        n = len(polygon_vertices)
        for i in range(n):
            p1 = polygon_vertices[i]
            p2 = polygon_vertices[(i + 1) % n]

            # 计算点到线段的最近点
            line_vec = p2 - p1
            point_vec = point - p1

            line_len_sq = np.dot(line_vec, line_vec)
            if line_len_sq == 0:
                # 线段退化为点
                candidate = p1
            else:
                t = max(0, min(1, np.dot(point_vec, line_vec) / line_len_sq))
                candidate = p1 + t * line_vec

            distance = np.linalg.norm(point - candidate)
            if distance < min_distance:
                min_distance = distance
                closest_point = candidate

        return closest_point

    def _enforce_boundary_constraints(self):
        """
        强制执行边界约束，将越界的船只拉回到边界内
        """
        # 任务区域的顶点（按顺序）
        area_vertices = [
            Config.AREA_POINTS['A1'],
            Config.AREA_POINTS['A2'],
            Config.AREA_POINTS['A3'],
            Config.AREA_POINTS['A4'],
            Config.AREA_POINTS['A5'],
            Config.AREA_POINTS['A6'],
            Config.AREA_POINTS['A7'],
            Config.AREA_POINTS['A8']
        ]

        # 检查所有白方船只
        for boat in self.white_boats:
            if not boat.active:
                continue
            if not self._is_point_in_polygon(boat.pos, area_vertices):
                # 船只越界，将其拉回到最近的边界点
                boat.pos = self._get_closest_point_on_boundary(boat.pos, area_vertices)
                # 可选：给予惩罚或重置速度
                boat.vel = 0  # 停止移动作为惩罚

        # 检查所有白方无人机
        for drone in self.white_drones:
            if not drone.active or drone.on_boat:
                continue
            if not self._is_point_in_polygon(drone.pos, area_vertices):
                # 无人机越界，将其拉回到最近的边界点
                drone.pos = self._get_closest_point_on_boundary(drone.pos, area_vertices)
                # 可选：给予惩罚
                drone.vel = drone.min_speed  # 降低到最小速度

        # 检查所有黑方船只
        for boat in self.black_boats:
            if not boat.active:
                continue
            if not self._is_point_in_polygon(boat.pos, area_vertices):
                # 黑方船只越界，将其拉回到最近的边界点
                boat.pos = self._get_closest_point_on_boundary(boat.pos, area_vertices)
                boat.vel = 0  # 停止移动作为惩罚

    def _cross_line(self, pos, line):
        # 简单检查是否跨越线（假设线是y=constant或类似）
        return pos[1] < Config.BREAKTHROUGH_LINE[0][1]  # 假设向上跨越

    def render(self, mode='human'):
        if self.screen is None:
            pygame.init()
            self.screen = pygame.display.set_mode((Config.SCREEN_WIDTH, Config.SCREEN_HEIGHT))

        self.screen.fill(Config.LIGHT_BLUE)

        # 缩放坐标
        def scale_pos(pos):
            return (int((pos[0] + Config.SCREEN_WIDTH / 2)), int(Config.SCREEN_HEIGHT - (pos[1] + Config.SCREEN_HEIGHT / 5)))

        # 绘制区域
        points = [scale_pos(Config.AREA_POINTS[p]) for p in ['A1', 'A2', 'A3', 'A4', 'A5', 'A6', 'A7', 'A8', 'A1']]
        pygame.draw.lines(self.screen, Config.BLACK, False, points, 2)

        # 绘制红线
        pygame.draw.line(self.screen, Config.RED, scale_pos(Config.BREAKTHROUGH_LINE[0]), scale_pos(Config.BREAKTHROUGH_LINE[1]), 3)

        arc_surface = pygame.Surface(self.screen.get_size(), pygame.SRCALPHA)
        # 绘制白方艇探测圆
        for boat in self.white_boats:
            if not boat.active:
                continue
            center = scale_pos(boat.pos)
            radius = int(boat.detect_range)
            pygame.draw.circle(arc_surface, (0, 0, 255, 50), center, radius)  # 半透明蓝圈
            
        # 绘制白方无人机扇形
        for drone in self.white_drones:
            if not drone.active:
                continue
            center = scale_pos(drone.pos)
            radius = int(drone.detect_range)
            start_angle = drone.heading - drone.detect_angle / 2
            end_angle = drone.heading + drone.detect_angle / 2
            points = [center]
            num_points = 20  # 弧线平滑度
            for i in range(num_points + 1):
                angle = start_angle + (end_angle - start_angle) * (i / num_points)
                x = center[0] + radius * math.cos(angle)
                y = center[1] - radius * math.sin(angle)
                points.append((x, y))
            pygame.draw.polygon(arc_surface, (0, 255, 0, 50), points)
            # pygame.draw.arc(self.screen, (0, 255, 0, 50), (center[0] - radius, center[1] - radius, 2 * radius, 2 * radius), start_angle, end_angle, 1)
        
        # 绘制黑方艇探测圆
        for boat in self.black_boats:
            if not boat.active:
                continue
            center = scale_pos(boat.pos)
            radius = int(boat.detect_range)
            pygame.draw.circle(arc_surface, (100, 0, 0, 50), center, radius)  # 半透明红圈
        self.screen.blit(arc_surface, (0, 0))

        # 绘制白方艇
        for i, boat in enumerate(self.white_boats):
            if not boat.active:
                continue
            color = Config.WHITE if boat.lock_count == 0 else Config.YELLOW
            pygame.draw.circle(self.screen, color, scale_pos(boat.pos), 6)

            # 绘制无人艇编号
            font = pygame.font.Font(None, 24)
            text = font.render(f'B{i+1}', True, Config.BLACK)
            text_pos = scale_pos(boat.pos)
            self.screen.blit(text, (text_pos[0] + 8, text_pos[1] - 8))

            # 绘制与配对无人机的连线
            if hasattr(boat, 'paired_drone') and boat.paired_drone and boat.paired_drone.active and not boat.paired_drone.on_boat:
                drone_pos = scale_pos(boat.paired_drone.pos)
                boat_pos = scale_pos(boat.pos)
                pygame.draw.line(self.screen, (100, 100, 100), boat_pos, drone_pos, 1)

        # 绘制巡逻点系统
        self._draw_patrol_points(scale_pos)

        # 绘制白方无人机
        for i, drone in enumerate(self.white_drones):
            if not drone.active:
                continue
            r = int(255 * (1-drone.energy))
            g = int(255 * drone.energy)
            color = (r, g, 0)

            # 跟踪模式时使用特殊颜色
            if drone.tracking_mode:
                color = (255, 165, 0)  # 橙色表示跟踪模式
            elif drone.on_boat:
                color = (128, 128, 128)  # 灰色表示在艇上

            center = scale_pos(drone.pos)
            size = 7
            pygame.draw.rect(self.screen, color, (center[0] - size // 2, center[1] - size // 2, size, size))

            # 绘制无人机编号
            font = pygame.font.Font(None, 20)
            text = font.render(f'D{i+1}', True, Config.BLACK)
            text_pos = scale_pos(drone.pos)
            self.screen.blit(text, (text_pos[0] + 8, text_pos[1] - 8))

            # 绘制跟踪连线
            if drone.tracking_mode and drone.tracking_target and drone.tracking_target.active:
                target_center = scale_pos(drone.tracking_target.pos)
                pygame.draw.line(self.screen, (255, 165, 0), center, target_center, 3)

            # 绘制到当前目标巡逻点的连线
            if hasattr(drone, 'patrol_waypoints') and drone.patrol_waypoints and not drone.tracking_mode:
                if hasattr(drone, 'current_patrol_index'):
                    current_target = drone.patrol_waypoints[drone.current_patrol_index]
                    target_pos = scale_pos(current_target)
                    pygame.draw.line(self.screen, (0, 255, 255), center, target_pos, 2)  # 青色连线

        # 绘制黑方艇
        for i, boat in enumerate(self.black_boats):
            if not boat.active:
                continue
            color = Config.BLACK if boat.lock_count == 0 else Config.RED
            pygame.draw.circle(self.screen, color, scale_pos(boat.pos), 8)

            # 绘制黑方艇编号和状态
            font = pygame.font.Font(None, 24)
            status = f'E{i+1}'
            if boat.lock_count > 0:
                status += f'(L{boat.lock_count})'
            text = font.render(status, True, Config.RED)
            text_pos = scale_pos(boat.pos)
            self.screen.blit(text, (text_pos[0] + 10, text_pos[1] - 10))

        
        pygame.display.flip()

    def close(self):
        if self.screen is not None:
            pygame.quit()
